{"0": {"id": "0", "target": null, "source": null, "parent": null, "children": null, "mxObjectId": "mxCell#69"}, "1": {"id": "1", "parent": null, "target": null, "source": null, "children": null, "mxObjectId": "mxCell#68"}, "init": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/init.png) no-repeat left center;background-size: 14px 14px;\">流程初始化</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"流程初始化\">流程初始化</li></ul>", "geometry": {"x": 150, "y": 350, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "init", "parent": null, "vertex": "true", "nodeType": 2, "nodeName": "流程初始化", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#70"}, "start": {"value": "<p style=\"margin:0;padding:0 0 0 18px;background:url(static/flowDesigner/stencils/clipart/start.png) no-repeat left center;background-size: 14px 14px;\">流程开始</p>", "geometry": {"x": 165, "y": 150, "width": 150, "height": 40, "TRANSLATE_CONTROL_POINTS": "true", "alternateBounds": null, "sourcePoint": null, "targetPoint": null, "points": null, "offset": null, "relative": false}, "style": "shadow=0;fillColor=#1DCC8F;strokeColor=white;fontColor=#FFFFFF;rounded=1;arcSize=50;fontSize=14;fontFamily=Microsoft YaHei;right;html=1;", "id": "start", "parent": null, "vertex": "true", "nodeType": 1, "nodeName": "Start", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#71"}, "recall": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "geometry": {"x": -850, "y": 550, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "recall", "parent": null, "vertex": "true", "nodeType": 4, "nodeName": "重新提交", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#72"}, "byStart": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\"></span></p>", "geometry": {"x": 150, "y": 566, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true", "alternateBounds": null, "sourcePoint": null, "targetPoint": null, "points": null, "offset": null, "relative": false}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "byStart", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "byStart_fromEBPM_byStart", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#73"}, "startToInit": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 210, "y": 170}, {"x": 210, "y": 232.5}, {"x": 210, "y": 232.5}, {"x": 210, "y": 295}]}, "id": "startToInit", "parent": null, "source": {"value": "<p style=\"margin:0;padding:0 0 0 18px;background:url(static/flowDesigner/stencils/clipart/start.png) no-repeat left center;background-size: 14px 14px;\">流程开始</p>", "geometry": {"x": 165, "y": 150, "width": 150, "height": 40, "TRANSLATE_CONTROL_POINTS": "true", "alternateBounds": null, "sourcePoint": null, "targetPoint": null, "points": null, "offset": null, "relative": false}, "style": "shadow=0;fillColor=#1DCC8F;strokeColor=white;fontColor=#FFFFFF;rounded=1;arcSize=50;fontSize=14;fontFamily=Microsoft YaHei;right;html=1;", "id": "start", "parent": null, "vertex": "true", "nodeType": 1, "nodeName": "Start", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#71"}, "target": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/init.png) no-repeat left center;background-size: 14px 14px;\">流程初始化</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"流程初始化\">流程初始化</li></ul>", "geometry": {"x": 150, "y": 350, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "init", "parent": null, "vertex": "true", "nodeType": 2, "nodeName": "流程初始化", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#70"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#74"}, "initToClient": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 230, "y": 405}, {"x": 230, "y": 450}, {"x": 230, "y": 450}, {"x": 230, "y": 495}]}, "id": "initToClient", "parent": null, "source": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/init.png) no-repeat left center;background-size: 14px 14px;\">流程初始化</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"流程初始化\">流程初始化</li></ul>", "geometry": {"x": 150, "y": 350, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "init", "parent": null, "vertex": "true", "nodeType": 2, "nodeName": "流程初始化", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#70"}, "target": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\"></span></p>", "geometry": {"x": 150, "y": 566, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true", "alternateBounds": null, "sourcePoint": null, "targetPoint": null, "points": null, "offset": null, "relative": false}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "byStart", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "byStart_fromEBPM_byStart", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#73"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#75"}, "initToRecall": {"value": "false", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 60, "y": 350}, {"x": -400, "y": 480}, {"x": -760, "y": 550}]}, "id": "initToRecall", "parent": null, "source": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/init.png) no-repeat left center;background-size: 14px 14px;\">流程初始化</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"流程初始化\">流程初始化</li></ul>", "geometry": {"x": 150, "y": 350, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "init", "parent": null, "vertex": "true", "nodeType": 2, "nodeName": "流程初始化", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#70"}, "target": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "geometry": {"x": -850, "y": 550, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "recall", "parent": null, "vertex": "true", "nodeType": 4, "nodeName": "重新提交", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#72"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#76"}, "recallToClient": {"value": "reSubmit", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": -760, "y": 565}, {"x": -350, "y": 565}, {"x": -350, "y": 565}, {"x": 60, "y": 565}]}, "id": "recallToClient", "parent": null, "source": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "geometry": {"x": -850, "y": 550, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "recall", "parent": null, "vertex": "true", "nodeType": 4, "nodeName": "重新提交", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#72"}, "target": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\"></span></p>", "geometry": {"x": 150, "y": 566, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true", "alternateBounds": null, "sourcePoint": null, "targetPoint": null, "points": null, "offset": null, "relative": false}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "byStart", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "byStart_fromEBPM_byStart", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#73"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#77"}, "06f9fe94-c1be-0959-1b40-459e1c343d1b": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b</span></p>", "geometry": {"x": 150, "y": 1350, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "06f9fe94-c1be-0959-1b40-459e1c343d1b", "parent": null, "vertex": "true", "nodeType": 6, "nodeName": "审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#78"}, "5bfb34a8-a16d-2d3e-5617-809bcbda5695": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695</span></p>", "geometry": {"x": 150, "y": 1150, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "5bfb34a8-a16d-2d3e-5617-809bcbda5695", "parent": null, "vertex": "true", "nodeType": 6, "nodeName": "审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#79"}, "e53d74da-8f5b-8b3e-a1b8-a901a777ed03": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03</span></p>", "geometry": {"x": 150, "y": 750, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#80"}, "fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd</span></p>", "geometry": {"x": 150, "y": 950, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#81"}, "line0ce090f8-31a5-fc15-f281-6137e6a0b797": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 60, "y": 1350}, {"x": -400, "y": 920}, {"x": -760, "y": 550}]}, "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "id": "line0ce090f8-31a5-fc15-f281-6137e6a0b797", "parent": null, "source": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b</span></p>", "geometry": {"x": 150, "y": 1350, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "06f9fe94-c1be-0959-1b40-459e1c343d1b", "parent": null, "vertex": "true", "nodeType": 6, "nodeName": "审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#78"}, "target": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "geometry": {"x": -850, "y": 550, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "recall", "parent": null, "vertex": "true", "nodeType": 4, "nodeName": "重新提交", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#72"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#82"}, "line575b302a-117b-ea86-1594-11998c03958e": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 190, "y": 805}, {"x": 190, "y": 850}, {"x": 190, "y": 850}, {"x": 190, "y": 895}]}, "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "id": "line575b302a-117b-ea86-1594-11998c03958e", "parent": null, "source": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03</span></p>", "geometry": {"x": 150, "y": 750, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#80"}, "target": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd</span></p>", "geometry": {"x": 150, "y": 950, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#81"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#83"}, "line802fef4f-aa7d-e099-34a6-e545b76c4b6e": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 230, "y": 1221}, {"x": 230, "y": 1250}, {"x": 230, "y": 1250}, {"x": 230, "y": 1279}]}, "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "id": "line802fef4f-aa7d-e099-34a6-e545b76c4b6e", "parent": null, "source": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695</span></p>", "geometry": {"x": 150, "y": 1150, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "5bfb34a8-a16d-2d3e-5617-809bcbda5695", "parent": null, "vertex": "true", "nodeType": 6, "nodeName": "审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#79"}, "target": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b</span></p>", "geometry": {"x": 150, "y": 1350, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "06f9fe94-c1be-0959-1b40-459e1c343d1b", "parent": null, "vertex": "true", "nodeType": 6, "nodeName": "审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#78"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#84"}, "line9f50482b-68e6-dcc6-27ab-619343addd64": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 60, "y": 1150}, {"x": -400, "y": 820}, {"x": -760, "y": 550}]}, "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "id": "line9f50482b-68e6-dcc6-27ab-619343addd64", "parent": null, "source": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695</span></p>", "geometry": {"x": 150, "y": 1150, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "5bfb34a8-a16d-2d3e-5617-809bcbda5695", "parent": null, "vertex": "true", "nodeType": 6, "nodeName": "审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#79"}, "target": {"value": "<p class=\"k2-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "geometry": {"x": -850, "y": 550, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "recall", "parent": null, "vertex": "true", "nodeType": 4, "nodeName": "重新提交", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#72"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#85"}, "lineea70b8e0-4e08-2563-1457-5aa7d133b722": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 150, "y": 1005}, {"x": 150, "y": 1042}, {"x": 150, "y": 1042}, {"x": 150, "y": 1079}]}, "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "id": "lineea70b8e0-4e08-2563-1457-5aa7d133b722", "parent": null, "source": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd</span></p>", "geometry": {"x": 150, "y": 950, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#81"}, "target": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695</span></p>", "geometry": {"x": 150, "y": 1150, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "5bfb34a8-a16d-2d3e-5617-809bcbda5695", "parent": null, "vertex": "true", "nodeType": 6, "nodeName": "审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#79"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#86"}, "occid7938771f-7426-42e7-8010-edf92d0b3694": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">流程已结束_fromEBPM_occid7938771f-7426-42e7-8010-edf92d0b3694</span></p>", "geometry": {"x": 150, "y": 1550, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "occid7938771f-7426-42e7-8010-edf92d0b3694", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "流程已结束_fromEBPM_occid7938771f-7426-42e7-8010-edf92d0b3694", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#87"}, "lineec79b8f2-3474-336-42e7-8010-edf92d0b3694": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 170, "y": 1421}, {"x": 170, "y": 1458}, {"x": 170, "y": 1458}, {"x": 170, "y": 1495}]}, "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "id": "lineec79b8f2-3474-336-42e7-8010-edf92d0b3694", "parent": null, "source": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b</span></p>", "geometry": {"x": 150, "y": 1350, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "06f9fe94-c1be-0959-1b40-459e1c343d1b", "parent": null, "vertex": "true", "nodeType": 6, "nodeName": "审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#78"}, "target": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">流程已结束_fromEBPM_occid7938771f-7426-42e7-8010-edf92d0b3694</span></p>", "geometry": {"x": 150, "y": 1550, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "occid7938771f-7426-42e7-8010-edf92d0b3694", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "流程已结束_fromEBPM_occid7938771f-7426-42e7-8010-edf92d0b3694", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#87"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#88"}, "linebyStarte53d74da-8f5b-8b3e-a1b8-a901a777ed03": {"value": "", "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 170, "y": 605}, {"x": 170, "y": 650}, {"x": 170, "y": 650}, {"x": 170, "y": 695}]}, "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "id": "linebyStarte53d74da-8f5b-8b3e-a1b8-a901a777ed03", "parent": null, "source": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\"></span></p>", "geometry": {"x": 150, "y": 566, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true", "alternateBounds": null, "sourcePoint": null, "targetPoint": null, "points": null, "offset": null, "relative": false}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "byStart", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "byStart_fromEBPM_byStart", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#73"}, "target": {"value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03</span></p>", "geometry": {"x": 150, "y": 750, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "id": "e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "parent": null, "vertex": "true", "nodeType": 9, "nodeName": "用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "connectable": "true", "target": null, "source": null, "edges": null, "mxObjectId": "mxCell#80"}, "edge": "true", "nodeType": 99, "mxObjectId": "mxCell#89"}}